<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>职位详情 - 高级前端开发工程师</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
          "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
        background-color: #f5f5f5;
        color: #333;
        line-height: 1.6;
      }

      /* 顶部状态栏 */
      .status-bar {
        background: #000;
        color: #fff;
        padding: 8px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
      }

      .status-left {
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .status-right {
        display: flex;
        align-items: center;
        gap: 3px;
      }

      .signal-bars {
        display: flex;
        gap: 2px;
      }

      .bar {
        width: 3px;
        background: #fff;
        border-radius: 1px;
      }

      .bar:nth-child(1) {
        height: 4px;
      }
      .bar:nth-child(2) {
        height: 6px;
      }
      .bar:nth-child(3) {
        height: 8px;
      }
      .bar:nth-child(4) {
        height: 10px;
      }

      .wifi-icon,
      .battery-icon {
        width: 15px;
        height: 10px;
        background: #fff;
        border-radius: 2px;
        position: relative;
      }

      .battery-icon::after {
        content: "";
        position: absolute;
        right: -3px;
        top: 3px;
        width: 2px;
        height: 4px;
        background: #fff;
        border-radius: 0 1px 1px 0;
      }

      /* 导航栏 */
      .navbar {
        background: #fff;
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        border-bottom: 1px solid #e5e5e5;
      }

      .back-btn {
        position: absolute;
        left: 20px;
        background: none;
        border: none;
        font-size: 18px;
        color: #333;
        cursor: pointer;
      }

      .nav-title {
        font-size: 17px;
        font-weight: 600;
        color: #333;
      }

      /* 主要内容 */
      .container {
        background: #fff;
        margin: 0;
        padding: 0;
      }

      /* 职位头部信息 */
      .job-header {
        padding: 20px;
        border-bottom: 8px solid #f5f5f5;
      }

      .job-title {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
      }

      .job-meta {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #666;
      }

      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .location-icon,
      .eye-icon,
      .time-icon {
        width: 14px;
        height: 14px;
        opacity: 0.6;
      }

      .publish-time {
        font-size: 13px;
        color: #999;
        margin-bottom: 15px;
      }

      .salary {
        font-size: 24px;
        font-weight: 700;
        color: #ff4757;
        margin-bottom: 20px;
      }

      /* 公司信息 */
      .company-info {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 20px;
        border-bottom: 8px solid #f5f5f5;
      }

      .company-logo {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-weight: bold;
        font-size: 18px;
      }

      .company-details h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .company-meta {
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
      }

      .company-desc {
        font-size: 13px;
        color: #999;
        line-height: 1.5;
      }

      /* 职位描述 */
      .job-section {
        padding: 20px;
        border-bottom: 8px solid #f5f5f5;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
      }

      .job-desc {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 15px;
      }

      .job-list {
        list-style: none;
        padding: 0;
      }

      .job-list li {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 8px;
        padding-left: 8px;
        position: relative;
      }

      .job-list li::before {
        content: "•";
        color: #999;
        position: absolute;
        left: 0;
      }

      /* 底部按钮 */
      .bottom-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        padding: 15px 20px 25px;
        border-top: 1px solid #e5e5e5;
        display: flex;
        gap: 15px;
      }

      .action-btn {
        flex: 1;
        padding: 12px;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .favorite-btn {
        background: #f8f9fa;
        color: #666;
        border: 1px solid #e5e5e5;
        flex: 0 0 50px;
      }

      .apply-btn {
        background: #007aff;
        color: #fff;
      }

      .apply-btn:hover {
        background: #0056cc;
      }

      .favorite-btn:hover {
        background: #e9ecef;
      }

      /* 响应式设计 */
      @media (max-width: 375px) {
        .job-title {
          font-size: 18px;
        }

        .salary {
          font-size: 22px;
        }

        .company-logo {
          width: 45px;
          height: 45px;
          font-size: 16px;
        }
      }

      /* 添加一些动画效果 */
      .container {
        animation: slideUp 0.3s ease-out;
      }

      @keyframes slideUp {
        from {
          transform: translateY(20px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }

      .action-btn {
        transition: transform 0.2s ease;
      }

      .action-btn:active {
        transform: scale(0.98);
      }
    </style>
  </head>
  <body>
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span>9:41</span>
      </div>
      <div class="status-right">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
        <div class="wifi-icon"></div>
        <div class="battery-icon"></div>
      </div>
    </div>

    <!-- 导航栏 -->
    <div class="navbar">
      <button class="back-btn" onclick="history.back()">‹</button>
      <div class="nav-title">职位详情</div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <!-- 职位头部信息 -->
      <div class="job-header">
        <h1 class="job-title">高级前端开发工程师</h1>
        <div class="job-meta">
          <div class="meta-item">
            <span class="location-icon">📍</span>
            <span>十堰·张湾</span>
          </div>
          <div class="meta-item">
            <span class="eye-icon">👁</span>
            <span>本科</span>
          </div>
        </div>
        <div class="publish-time">发布于1小时前</div>
        <div class="salary">25K-40K</div>
      </div>

      <!-- 公司信息 -->
      <div class="company-info">
        <div class="company-logo">字</div>
        <div class="company-details">
          <h3>字节跳动</h3>
          <div class="company-meta">民营企业 | 1000人以上</div>
          <div class="company-desc">
            字节跳动成立于2012年3月，是一家全球化的互联网科技公司。公司致力于技术驱动产品创新，为全球用户提供有价值的信息交流的数字平台。旗下产品包括今日头条、抖音、TikTok等。
          </div>
        </div>
      </div>

      <!-- 职位描述 -->
      <div class="job-section">
        <h2 class="section-title">职位描述</h2>
        <div class="job-desc">
          负责公司核心产品的前端架构设计和开发，优化用户体验，参与技术决策。
        </div>
        <ul class="job-list">
          <li>负责公司核心产品的前端架构设计和开发</li>
          <li>优化前端性能，提升用户体验</li>
          <li>参与产品需求分析，提供技术解决方案</li>
          <li>编写高质量、可维护的前端代码</li>
          <li>与后端工程师协作，完成项目开发</li>
        </ul>
      </div>

      <!-- 职位要求 -->
      <div class="job-section">
        <h2 class="section-title">职位要求</h2>
        <ul class="job-list">
          <li>本科及以上学历，计算机相关专业优先</li>
          <li>3年以上前端开发经验，熟练掌握HTML、CSS、JavaScript</li>
          <li>熟悉React、Vue等主流前端框架</li>
          <li>熟悉前端工程化工具，如Webpack、Vite等</li>
          <li>具备良好的代码规范和团队协作能力</li>
          <li>有移动端开发经验者优先</li>
          <li>有Node.js开发经验者优先</li>
        </ul>
      </div>

      <!-- 公司福利 -->
      <div class="job-section">
        <h2 class="section-title">公司福利</h2>
        <ul class="job-list">
          <li>五险一金，补充商业保险</li>
          <li>年终奖金，绩效奖励</li>
          <li>带薪年假，弹性工作时间</li>
          <li>免费三餐，下午茶</li>
          <li>定期团建活动</li>
          <li>技术培训，职业发展规划</li>
          <li>股票期权</li>
        </ul>
      </div>

      <!-- 工作地点 -->
      <div class="job-section">
        <h2 class="section-title">工作地点</h2>
        <div class="job-desc">
          湖北省十堰市张湾区车城西路特1号华为云数据中心
        </div>
        <div class="job-desc" style="color: #999; font-size: 13px">
          地铁1号线张湾站A出口步行5分钟
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-actions">
      <button class="action-btn favorite-btn">⭐</button>
      <button class="action-btn apply-btn">申请职位</button>
    </div>

    <script>
      // 添加一些交互效果
      document.addEventListener("DOMContentLoaded", function () {
        // 申请按钮点击效果
        const applyBtn = document.querySelector(".apply-btn");
        applyBtn.addEventListener("click", function () {
          alert("申请已提交！");
        });

        // 收藏按钮点击效果
        const favoriteBtn = document.querySelector(".favorite-btn");
        let isFavorited = false;

        favoriteBtn.addEventListener("click", function () {
          isFavorited = !isFavorited;
          this.textContent = isFavorited ? "★" : "⭐";
          this.style.color = isFavorited ? "#ff4757" : "#666";
        });

        // 返回按钮功能
        const backBtn = document.querySelector(".back-btn");
        backBtn.addEventListener("click", function () {
          if (window.history.length > 1) {
            window.history.back();
          } else {
            window.location.href = "index.html";
          }
        });
      });
    </script>
  </body>
</html>
